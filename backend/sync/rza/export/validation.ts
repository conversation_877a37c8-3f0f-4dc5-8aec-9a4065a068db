/**
 * Validation functions for RZA export according to API contract
 * Implements the exact validation requirements specified in the API contract
 */

import { RzaArticle } from './models/article.model';
import { RzaCustomer } from './models/customer.model';
import { RzaCategory } from './models/category.model';

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validates RZA article according to API contract requirements
 * API Contract validation rules:
 * - Pflichtfelder: ordernumber, name, rzaArtikelID
 * - Preisvalidierung: Mindestens ein Preis mit from: 1 muss vorhanden sein
 * - Status-Validierung: active und onlineshopstatus müssen numerische Werte (0 oder 1) sein
 * - Gewicht-Validierung: Muss numerischer Wert ≥ 0 sein
 * - Kategorie-Validierung: Referenzierte Kategorien müssen existieren
 * 
 * @param article - RZA article to validate
 * @param availableCategories - Available categories for reference validation
 * @returns Validation result
 */
export function validateRzaArticle(
  article: RzaArticle,
  availableCategories: RzaCategory[] = []
): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // API Contract: Pflichtfelder validation
  if (!article.ordernumber || article.ordernumber.trim() === '') {
    errors.push('Order number (ordernumber) is required');
  }

  if (!article.name || article.name.trim() === '') {
    errors.push('Product name (name) is required');
  }

  if (!article.rzaArtikelID || article.rzaArtikelID <= 0) {
    errors.push('RZA Article ID (rzaArtikelID) is required and must be positive');
  }

  // API Contract: Preisvalidierung - Mindestens ein Preis mit from: 1 muss vorhanden sein
  const basePrice = article.prices?.find(price => price.from === 1);
  if (!basePrice) {
    errors.push('At least one price with from: 1 must be present');
  }

  // API Contract: Status-Validierung - active und onlineshopstatus müssen numerische Werte (0 oder 1) sein
  if (article.active !== 0 && article.active !== 1) {
    errors.push('Active status must be 0 or 1');
  }

  if (article.onlineshopstatus !== 0 && article.onlineshopstatus !== 1) {
    errors.push('Online shop status must be 0 or 1');
  }

  // API Contract: Gewicht-Validierung - Muss numerischer Wert ≥ 0 sein
  if (typeof article.weight !== 'number' || article.weight < 0) {
    errors.push('Weight must be a numeric value ≥ 0');
  }

  // API Contract: Kategorie-Validierung - Referenzierte Kategorien müssen existieren
  if (availableCategories.length > 0 && article.categories.length > 0) {
    const availableCategoryIds = availableCategories.map(cat => cat.ID);
    const invalidCategories = article.categories.filter(catId => !availableCategoryIds.includes(catId));
    if (invalidCategories.length > 0) {
      errors.push(`Referenced categories do not exist: ${invalidCategories.join(', ')}`);
    }
  }

  // Additional validations for data quality
  if (!article.ean || article.ean.trim() === '') {
    warnings.push('EAN code is missing');
  }

  if (!article.unitID || article.unitID.trim() === '') {
    warnings.push('Unit ID is missing');
  }

  if (article.tax < 0 || article.tax > 100) {
    warnings.push('Tax rate should be between 0 and 100');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validates RZA customer according to API contract requirements
 * API Contract validation rules:
 * - Pflichtfelder: rzaAddressId, Zuname, Vorname
 * - E-Mail-Validierung: Gültiges E-Mail-Format
 * - Länder-Validierung: Ländercode muss in Mapping-Tabelle existieren
 * - Telefon-Validierung: Formatierung und Bereinigung von Sonderzeichen
 * 
 * @param customer - RZA customer to validate
 * @returns Validation result
 */
export function validateRzaCustomer(customer: RzaCustomer): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // API Contract: Pflichtfelder validation
  if (!customer.rzaAddressId || customer.rzaAddressId <= 0) {
    errors.push('RZA Address ID (rzaAddressId) is required and must be positive');
  }

  if (!customer.lastname || customer.lastname.trim() === '') {
    errors.push('Last name (Zuname) is required');
  }

  if (!customer.firstname || customer.firstname.trim() === '') {
    errors.push('First name (Vorname) is required');
  }

  // API Contract: E-Mail-Validierung - Gültiges E-Mail-Format
  if (customer.email && !isValidEmail(customer.email)) {
    errors.push('Email address format is invalid');
  }

  // API Contract: Länder-Validierung - Basic country code validation
  if (customer.country && customer.country.length !== 2) {
    warnings.push('Country code should be 2-letter ISO code');
  }

  // API Contract: Telefon-Validierung - Basic phone format validation
  if (customer.phone && !isValidPhoneFormat(customer.phone)) {
    warnings.push('Phone number format may be invalid');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validates RZA category according to API contract requirements
 * 
 * @param category - RZA category to validate
 * @returns Validation result
 */
export function validateRzaCategory(category: RzaCategory): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Basic required fields
  if (!category.ID || category.ID <= 0) {
    errors.push('Category ID is required and must be positive');
  }

  if (!category.name || category.name.trim() === '') {
    errors.push('Category name is required');
  }

  // Hierarchy validation
  if (category.ParentID && category.ParentID === category.ID) {
    errors.push('Category cannot be its own parent');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validates multiple RZA articles
 * 
 * @param articles - Array of RZA articles to validate
 * @param availableCategories - Available categories for reference validation
 * @returns Object with validation results
 */
export function validateRzaArticles(
  articles: RzaArticle[],
  availableCategories: RzaCategory[] = []
): {
  valid: RzaArticle[];
  invalid: Array<{ article: RzaArticle; validation: ValidationResult }>;
  totalErrors: number;
  totalWarnings: number;
} {
  const valid: RzaArticle[] = [];
  const invalid: Array<{ article: RzaArticle; validation: ValidationResult }> = [];
  let totalErrors = 0;
  let totalWarnings = 0;

  articles.forEach(article => {
    const validation = validateRzaArticle(article, availableCategories);
    totalErrors += validation.errors.length;
    totalWarnings += validation.warnings.length;

    if (validation.isValid) {
      valid.push(article);
    } else {
      invalid.push({ article, validation });
    }
  });

  return { valid, invalid, totalErrors, totalWarnings };
}

/**
 * Simple email validation
 * 
 * @param email - Email address to validate
 * @returns True if email format is valid
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Basic phone format validation
 * 
 * @param phone - Phone number to validate
 * @returns True if phone format appears valid
 */
function isValidPhoneFormat(phone: string): boolean {
  // Remove common separators and check if remaining characters are mostly digits
  const cleaned = phone.replace(/[\s\-\(\)\+]/g, '');
  return /^\d{6,15}$/.test(cleaned);
}

/**
 * Validates price structure according to API contract
 * 
 * @param prices - Array of prices to validate
 * @returns Validation result
 */
export function validatePriceStructure(prices: Array<{ price: number; from: number; pricegroup: string }>): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!prices || prices.length === 0) {
    errors.push('At least one price must be defined');
    return { isValid: false, errors, warnings };
  }

  // API Contract: Mindestens ein Preis mit from: 1 muss vorhanden sein
  const basePrice = prices.find(price => price.from === 1);
  if (!basePrice) {
    errors.push('At least one price with from: 1 must be present');
  }

  // Validate individual prices
  prices.forEach((price, index) => {
    if (price.price < 0) {
      errors.push(`Price ${index + 1}: Price must be ≥ 0`);
    }

    if (price.from < 1) {
      errors.push(`Price ${index + 1}: From quantity must be ≥ 1`);
    }

    if (!price.pricegroup || price.pricegroup.trim() === '') {
      errors.push(`Price ${index + 1}: Price group is required`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
