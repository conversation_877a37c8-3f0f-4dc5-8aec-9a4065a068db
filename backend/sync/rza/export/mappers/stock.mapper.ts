/**
 * Stock mapping functions for RZA to WooCommerce stock synchronization
 * Handles the 15-minute stock export according to API contract
 */

import { RzaArticle } from '../models/article.model';

/**
 * Simplified RZA article interface for stock export
 * According to API contract, only these fields are needed for stock sync
 */
export interface RzaStockArticle {
  ordernumber: string; // RZA: ordernumber -> WooCommerce: sku (direct)
  rzaArtikelID: number; // RZA: rzaArtikelID (for reference)
  instock: number; // RZA: instock -> WooCommerce: stock_quantity (Max(0, instock))
  stock?: number; // RZA: stock (actual stock level, optional)
  fields?: Array<{ fieldnumber: number; value: string }>; // RZA: fields (optional, may be present but not used)
}

/**
 * WooCommerce stock update interface
 * Minimal interface for stock-only updates
 */
export interface WooCommerceStockUpdate {
  sku: string; // Product SKU to identify the product
  stock_quantity: number; // New stock quantity
  stock_status: 'instock' | 'outofstock' | 'onbackorder'; // Stock status
  manage_stock: boolean; // Always true for stock management
}

/**
 * Configuration for stock mapping
 */
export interface StockMappingConfig {
  stockThreshold: number; // Minimum stock to consider in stock (default: 0)
  updateStockStatus: boolean; // Whether to update stock status based on quantity (default: true)
}

/**
 * Default configuration for stock mapping
 */
export const DEFAULT_STOCK_CONFIG: StockMappingConfig = {
  stockThreshold: 0,
  updateStockStatus: true
};

/**
 * Maps a single RZA stock article to WooCommerce stock update
 * According to API contract field mapping:
 * - RZA ordernumber -> WooCommerce sku (direct)
 * - RZA instock -> WooCommerce stock_quantity (Max(0, instock))
 * 
 * @param rzaStockArticle - RZA stock article data
 * @param config - Stock mapping configuration
 * @returns WooCommerce stock update object
 */
export function mapRzaStockToWooStock(
  rzaStockArticle: RzaStockArticle,
  config: Partial<StockMappingConfig> = {}
): WooCommerceStockUpdate {
  const mappingConfig = { ...DEFAULT_STOCK_CONFIG, ...config };

  // API Contract: Max(0, instock) - ensure non-negative stock quantity
  const stockQuantity = Math.max(0, rzaStockArticle.instock);
  
  // Determine stock status based on quantity and threshold
  const stockStatus = mappingConfig.updateStockStatus
    ? (stockQuantity > mappingConfig.stockThreshold ? 'instock' : 'outofstock')
    : 'instock'; // Default to instock if not updating status

  return {
    sku: rzaStockArticle.ordernumber, // RZA: ordernumber -> WooCommerce: sku (direct)
    stock_quantity: stockQuantity, // RZA: instock -> WooCommerce: stock_quantity (Max(0, instock))
    stock_status: stockStatus,
    manage_stock: true
  };
}

/**
 * Maps multiple RZA stock articles to WooCommerce stock updates
 * 
 * @param rzaStockArticles - Array of RZA stock articles
 * @param config - Stock mapping configuration
 * @returns Array of WooCommerce stock updates
 */
export function mapRzaStocksToWooStocks(
  rzaStockArticles: RzaStockArticle[],
  config: Partial<StockMappingConfig> = {}
): WooCommerceStockUpdate[] {
  return rzaStockArticles
    .filter(article => shouldIncludeStockArticle(article))
    .map(article => mapRzaStockToWooStock(article, config));
}

/**
 * Maps full RZA article to stock article (for compatibility)
 * Extracts only the fields needed for stock synchronization
 * 
 * @param rzaArticle - Full RZA article
 * @returns RZA stock article with only relevant fields
 */
export function extractStockFromRzaArticle(rzaArticle: RzaArticle): RzaStockArticle {
  return {
    ordernumber: rzaArticle.ordernumber,
    rzaArtikelID: rzaArticle.rzaArtikelID,
    instock: rzaArticle.instock,
    stock: rzaArticle.stock,
    fields: rzaArticle.fields
  };
}

/**
 * Maps multiple full RZA articles to stock articles
 * 
 * @param rzaArticles - Array of full RZA articles
 * @returns Array of RZA stock articles
 */
export function extractStocksFromRzaArticles(rzaArticles: RzaArticle[]): RzaStockArticle[] {
  return rzaArticles.map(article => extractStockFromRzaArticle(article));
}

/**
 * Determines if a stock article should be included in the sync
 * 
 * @param article - RZA stock article to check
 * @returns True if article should be included
 */
function shouldIncludeStockArticle(article: RzaStockArticle): boolean {
  // Must have valid order number (SKU)
  if (!article.ordernumber || article.ordernumber.trim() === '') {
    return false;
  }

  // Must have valid RZA article ID
  if (!article.rzaArtikelID || article.rzaArtikelID <= 0) {
    return false;
  }

  // Stock quantity can be 0 or negative (will be normalized to 0)
  return true;
}

/**
 * Validates stock article data according to API contract
 * 
 * @param article - RZA stock article to validate
 * @returns Array of validation errors
 */
export function validateStockArticle(article: RzaStockArticle): string[] {
  const errors: string[] = [];

  // API Contract validation: ordernumber is required
  if (!article.ordernumber || article.ordernumber.trim() === '') {
    errors.push('Order number (ordernumber) is required');
  }

  // API Contract validation: rzaArtikelID is required
  if (!article.rzaArtikelID || article.rzaArtikelID <= 0) {
    errors.push('RZA Article ID (rzaArtikelID) is required and must be positive');
  }

  // API Contract validation: instock must be numeric
  if (typeof article.instock !== 'number') {
    errors.push('Stock quantity (instock) must be a number');
  }

  return errors;
}

/**
 * Validates multiple stock articles
 * 
 * @param articles - Array of RZA stock articles to validate
 * @returns Object with validation results
 */
export function validateStockArticles(articles: RzaStockArticle[]): {
  valid: RzaStockArticle[];
  invalid: Array<{ article: RzaStockArticle; errors: string[] }>;
} {
  const valid: RzaStockArticle[] = [];
  const invalid: Array<{ article: RzaStockArticle; errors: string[] }> = [];

  articles.forEach(article => {
    const errors = validateStockArticle(article);
    if (errors.length === 0) {
      valid.push(article);
    } else {
      invalid.push({ article, errors });
    }
  });

  return { valid, invalid };
}
