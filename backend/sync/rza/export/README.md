# RZA Export System

This directory contains the RZA export functionality for converting RZA ERP data to WooCommerce format. The system processes RZA XML exports and transforms them into WooCommerce-compatible data structures for seamless e-commerce integration.

## 🏗️ Architecture

The system has been restructured for better maintainability and follows domain separation principles with comprehensive TypeScript support:

### Directory Structure

```
backend/sync/rza/export/
├── models/                    # TypeScript model definitions
│   ├── common.model.ts       # Shared types (fields, prices, discounts, etc.)
│   ├── article.model.ts      # Article/product models and configurations
│   ├── customer.model.ts     # Customer models and address structures
│   ├── articleGroup.model.ts # Article group models with discount support
│   └── category.model.ts     # Category models with hierarchy support
├── mappers/                  # Domain-specific mapping functions
│   ├── articles.mapper.ts    # Article to WooCommerce product mapping
│   ├── customers.mapper.ts   # Customer to WooCommerce customer mapping
│   └── articleGroups.mapper.ts # Category/group to WooCommerce category mapping
├── exportService.ts          # Main orchestration service with XML parsing
├── testExport.ts            # Test demonstrations and usage examples
└── full_export.example.xml  # Complete RZA export XML structure example
```

## 🚀 Quick Start

### Basic Usage

```typescript
import {
  mapRzaArticleToWooProduct,
  mapRzaCustomerToWooCustomer,
  mapRzaCategoriesToWooCategories,
  mapRzaGroupsToWooCategories
} from './mappers/articles.mapper';
import { RzaExportService } from './exportService';

// Map a single article with configuration
const wooProduct = mapRzaArticleToWooProduct(rzaArticle, categories, {
  defaultPriceGroup: 'VK-Preis',
  includeInactiveProducts: false,
  stockThreshold: 5,
  defaultTaxClass: 'standard'
});

// Map a customer with address synchronization
const wooCustomer = mapRzaCustomerToWooCustomer(rzaCustomer, {
  defaultRole: 'customer',
  createUsername: true,
  syncBillingAddress: true,
  syncShippingAddress: true,
  defaultCountryCode: 'AT'
});

// Map categories with hierarchy and discount information
const wooCategories = mapRzaCategoriesToWooCategories(rzaCategories, {
  createHierarchy: true,
  slugPrefix: 'rza-',
  includeDiscounts: true,
  maxDepth: 5
});

// Map article groups to categories
const wooGroupCategories = mapRzaGroupsToWooCategories(groupDefinition, {
  createHierarchy: true,
  includeDiscounts: true,
  defaultDisplay: 'products'
});
```

### Using the Export Service

```typescript
import { RzaExportService } from './exportService';

const exportService = new RzaExportService({
  xmlFilePath: './data/rza_export.xml',
  outputDirectory: './tmp/rza-export',

  // Export configuration
  exportArticles: true,
  exportCustomers: true,
  exportCategories: true,
  exportGroups: true,

  // Output options
  generateJsonFiles: true,
  generateCsvFiles: false,
  logProgress: true,

  // Mapping configurations
  articleConfig: {
    defaultPriceGroup: 'VK-Preis',
    includeInactiveProducts: false,
    stockThreshold: 0
  },
  customerConfig: {
    defaultRole: 'customer',
    createUsername: true,
    defaultCountryCode: 'AT'
  },
  categoryConfig: {
    createHierarchy: true,
    slugPrefix: 'rza-',
    includeDiscounts: false
  }
});

const result = await exportService.executeExport();
console.log(`Export completed: ${result.message}`);
console.log(`Processed ${result.stats.articlesProcessed} articles, ${result.stats.customersProcessed} customers`);
console.log(`Output files: ${result.outputFiles.join(', ')}`);
```

## 📋 Features

### Article Mapping
- ✅ Complete product information mapping
- ✅ Multi-tier pricing with quantity breaks
- ✅ Tax rate handling per country
- ✅ Stock management and availability
- ✅ Custom fields preservation
- ✅ Textile article variants support
- ✅ Multi-language translations
- ✅ Category assignments
- ✅ Supplier information

### Customer Mapping
- ✅ Complete customer profile mapping
- ✅ Billing and shipping addresses
- ✅ Price group assignments
- ✅ Special price agreements
- ✅ Credit limits and delivery blocks
- ✅ VAT number handling
- ✅ Custom customer fields
- ✅ Username generation
- ✅ Country code mapping

### Category Mapping
- ✅ Hierarchical category structure
- ✅ Article group to category mapping
- ✅ Sub-group handling
- ✅ Discount information inclusion
- ✅ SEO-friendly slug generation
- ✅ Menu ordering

## 🔧 Configuration Options

### Article Mapping Configuration

```typescript
interface ArticleMappingConfig {
  defaultPriceGroup: string;        // Default price group (e.g., 'VK-Preis')
  defaultLanguageId: number;        // Default language for translations (default: 1)
  includeInactiveProducts: boolean; // Include inactive products (default: false)
  stockThreshold: number;           // Minimum stock for 'instock' status (default: 0)
  defaultTaxClass: string;          // Default WooCommerce tax class (default: '')
}

// Default configuration
const DEFAULT_ARTICLE_CONFIG = {
  defaultPriceGroup: 'VK-Preis',
  defaultLanguageId: 1,
  includeInactiveProducts: false,
  stockThreshold: 0,
  defaultTaxClass: ''
};
```

### Customer Mapping Configuration

```typescript
interface CustomerMappingConfig {
  defaultRole: string;              // Default WooCommerce user role (default: 'customer')
  createUsername: boolean;          // Auto-generate usernames (default: true)
  sendWelcomeEmail: boolean;        // Send welcome emails (default: false)
  syncBillingAddress: boolean;      // Sync billing address (default: true)
  syncShippingAddress: boolean;     // Sync shipping address (default: true)
  defaultCountryCode: string;       // Default country code (default: 'AT')
}

// Default configuration
const DEFAULT_CUSTOMER_CONFIG = {
  defaultRole: 'customer',
  createUsername: true,
  sendWelcomeEmail: false,
  syncBillingAddress: true,
  syncShippingAddress: true,
  defaultCountryCode: 'AT'
};
```

### Category Mapping Configuration

```typescript
interface CategoryMappingConfig {
  createHierarchy: boolean;         // Maintain parent-child relationships (default: true)
  maxDepth: number;                // Maximum category depth (default: 5)
  slugPrefix: string;              // Prefix for category slugs (default: 'rza-')
  slugSuffix: string;              // Suffix for category slugs (default: '')
  includeIdInSlug: boolean;        // Include RZA ID in slug (default: false)
  defaultDisplay: string;          // Default category display type (default: 'default')
  defaultDescription: string;      // Default description template (default: '')
  includeDiscounts: boolean;       // Include discount information (default: false)
  updateExisting: boolean;         // Update existing categories (default: true)
  deleteOrphaned: boolean;         // Delete categories not in RZA (default: false)
  preserveCustomFields: boolean;   // Preserve WooCommerce custom fields (default: true)
}

// Default configuration
const DEFAULT_CATEGORY_CONFIG = {
  createHierarchy: true,
  maxDepth: 5,
  slugPrefix: 'rza-',
  slugSuffix: '',
  includeIdInSlug: false,
  defaultDisplay: 'default',
  defaultDescription: '',
  includeDiscounts: false,
  updateExisting: true,
  deleteOrphaned: false,
  preserveCustomFields: true
};
```

## 📊 Data Mapping Details

### Article Field Mapping

The article mapper (`articles.mapper.ts`) performs comprehensive field mapping from RZA articles to WooCommerce products:

| RZA Field | WooCommerce Field | Transformation Logic | Notes |
|-----------|-------------------|---------------------|-------|
| `name` | `name` | Direct mapping | Product display name |
| `ordernumber` | `sku` | Direct mapping | Unique product identifier |
| `ordernumber` + `name` | `slug` | URL-safe conversion | Generated using `generateProductSlug()` |
| `description_long` | `description` | HTML cleaning | CDATA wrapper removed, HTML preserved |
| `description_long` + fields | `short_description` | Auto-generated | Uses custom fields or generated summary |
| `instock` | `stock_quantity` | Max(0, instock) | Available quantity (not actual stock) |
| `stock` | `meta_data._rza_stock` | Direct mapping | Actual stock level preserved |
| `weight` | `weight` | String conversion | Product weight in kg |
| `active` + `onlineshopstatus` | `status` | Combined logic | 'publish' if both are 1, 'draft' otherwise |
| `categories` | `categories` | Category lookup | Maps IDs to category objects with names |
| `tax` | `tax_status` + `tax_class` | Tax configuration | 'taxable' if tax > 0, 'none' otherwise |
| `textilartikel.kennzeichen` | `type` | Product type logic | 'variable' for main textile articles, 'simple' otherwise |

### Price Mapping Logic

The price mapping system (`findBestPrice()`) handles complex RZA pricing structures:

#### Price Group Selection
- **Primary Price**: Searches for `pricegroup` with `from: 1` (base quantity)
- **Sale Price**: Searches for `pricegroup` with `from: 10` (quantity break)
- **Price Groups**: Supports multiple customer price groups (VK-Preis, VK-DE-Preis, etc.)

#### Quantity Break Logic
```typescript
// Example: Find best price for quantity 25
const prices = [
  { price: 10.75, pricegroup: 'VK-Preis', from: 1 },
  { price: 9.10, pricegroup: 'VK-Preis', from: 10 },
  { price: 8.58, pricegroup: 'VK-Preis', from: 48 }
];
// Returns: { price: 9.10, from: 10 } for quantity 25
```

#### WooCommerce Price Fields
- `regular_price`: Base price from primary price group
- `sale_price`: Set only if quantity break price is lower than regular price
- `price`: Current effective price (regular or sale)

### Meta Data Preservation

All RZA-specific data is preserved in WooCommerce meta fields for complete data integrity:

#### Core Article Meta Data
- `_rza_artikel_id` - RZA article ID (rzaArtikelID)
- `_rza_art_group_id` - Article group ID (artGroupID)
- `_rza_art_sub_group_id` - Article sub-group ID (artSubGroupID)
- `_rza_unit_id` - Unit of measurement (unitID)
- `_rza_ean` - EAN barcode
- `_rza_barcode` - Additional barcode
- `_rza_tax_rate` - Tax rate percentage
- `_rza_shipping_time` - Delivery time information
- `_rza_stock` - Actual stock level (different from available quantity)

#### Supplier and Custom Data
- `_rza_supplier_numbers` - JSON array of supplier part numbers
- `_rza_field_1` to `_rza_field_10` - Custom fields (only populated fields)
- `_rza_textile_info` - JSON object with textile article data (if applicable)

### Customer Field Mapping

The customer mapper (`customers.mapper.ts`) transforms RZA customer data to WooCommerce customers with comprehensive address handling:

| RZA Field | WooCommerce Field | Transformation Logic | Notes |
|-----------|-------------------|---------------------|-------|
| `Mail` | `email` | Direct mapping or fallback | Generates fallback email if missing |
| `Vorname` | `first_name` | Direct mapping | Customer first name |
| `Zuname` | `last_name` | Direct mapping | Customer last name |
| `customernumber` / `Mail` / Names | `username` | Smart generation | Uses customer number, email prefix, or name combination |
| `pricegroup` | `meta_data._rza_price_group` | Direct mapping | Customer price group for pricing |

#### Address Mapping (Billing & Shipping)
| RZA Field | WooCommerce Field | Transformation Logic | Notes |
|-----------|-------------------|---------------------|-------|
| `Vorname` | `billing.first_name` | Direct mapping | First name in address |
| `Zuname` | `billing.last_name` | Direct mapping | Last name in address |
| `Namenszeile2` / `Namenszeile3` | `billing.company` | Company detection | Detects company indicators (GmbH, AG, etc.) |
| `Strasse` | `billing.address_1` | Direct mapping | Street address |
| `Namenszeile2` | `billing.address_2` | Conditional mapping | Additional address line if not company |
| `Ort` | `billing.city` | Direct mapping | City name |
| `PLZ` | `billing.postcode` | Direct mapping | Postal code |
| `Land` | `billing.country` | Country code mapping | Maps German/English names to ISO codes |
| `Telefon1` / `Telefon2` | `billing.phone` | First available | Primary or secondary phone |
| `Mail` | `billing.email` | Direct mapping | Email address |

#### Country Code Mapping
The system includes comprehensive country mapping for European markets:
```typescript
const countryMappings = {
  'Österreich': 'AT', 'Austria': 'AT',
  'Deutschland': 'DE', 'Germany': 'DE',
  'Schweiz': 'CH', 'Switzerland': 'CH',
  'Italien': 'IT', 'Italy': 'IT',
  'Frankreich': 'FR', 'France': 'FR',
  // ... and more European countries
};
```

#### Customer Meta Data Preservation
- `_rza_address_id` - Unique RZA customer/address ID
- `_rza_customer_number` - Shop customer number (if available)
- `_rza_price_group` - Customer price group
- `_rza_country_id` - RZA country ID reference
- `_rza_credit_limit` - Customer credit limit
- `_rza_delivery_block` - Delivery block status (0/1)
- `_rza_title` - Customer title (Dr., Prof., etc.)
- `_rza_salutation` - Salutation (Mr., Mrs., etc.)
- `_rza_vat_number` - VAT/UID number
- `_rza_phone_2` - Secondary phone number
- `_rza_fax` - Fax number
- `_rza_customer_field_1` to `_rza_customer_field_5` - Custom customer fields
- `_rza_total_discount` - JSON object with general discount configuration
- `_rza_price_agreements` - JSON array of special price agreements

### Category and Article Group Mapping

The article groups mapper (`articleGroups.mapper.ts`) handles both RZA categories and article groups with hierarchical support:

#### Article Group to Category Mapping
| RZA Field | WooCommerce Field | Transformation Logic | Notes |
|-----------|-------------------|---------------------|-------|
| `name` | `name` | Direct mapping | Group display name |
| `name` + `ID` | `slug` | URL-safe generation | Uses `generateCategorySlug()` with prefix |
| `number` | `menu_order` | Direct mapping | Display order in menus |
| `discounts` | `description` | Discount information | Optional discount details in description |

#### Category Hierarchy Mapping
| RZA Field | WooCommerce Field | Transformation Logic | Notes |
|-----------|-------------------|---------------------|-------|
| `ID` | `id` | Direct mapping | Category identifier |
| `name` | `name` | Direct mapping | Category display name |
| `ParentID` | `parent` | Hierarchy mapping | Parent-child relationships |
| `name` + `ID` | `slug` | URL-safe generation | Configurable prefix and suffix |

#### Hierarchy Processing
The system builds proper category hierarchies using `buildCategoryHierarchy()`:
1. **Tree Construction**: Creates parent-child relationships from flat arrays
2. **Level Calculation**: Assigns hierarchy levels for proper ordering
3. **Ordered Output**: Ensures parents are created before children
4. **Menu Ordering**: Assigns menu_order based on hierarchy level and position

#### Discount Integration
Article groups can include active discount information:
```typescript
// Example discount structure
const discount = {
  fromDate: '10.03.2018',
  toDate: '31.03.2018',
  percent: 5.00,
  pricegroup: 'VK-Preis'
};
```

The `extractActiveDiscounts()` function filters discounts by date range and provides:
- Group-based discounts (main groups and sub-groups)
- Price group specific discounts
- Date range validation
- Discount percentage and validity period

#### Category Configuration Options
```typescript
interface CategoryMappingConfig {
  createHierarchy: boolean;     // Maintain parent-child relationships
  maxDepth: number;            // Maximum category depth (default: 5)
  slugPrefix: string;          // Prefix for slugs (default: 'rza-')
  slugSuffix: string;          // Suffix for slugs
  includeIdInSlug: boolean;    // Include RZA ID in slug
  defaultDisplay: string;      // Category display type
  includeDiscounts: boolean;   // Include discount info in descriptions
  updateExisting: boolean;     // Update existing categories
  deleteOrphaned: boolean;     // Delete categories not in RZA
  preserveCustomFields: boolean; // Preserve WooCommerce custom fields
}
```

## 🔧 Export Service Architecture

The `RzaExportService` class orchestrates the complete export process from RZA XML to WooCommerce-compatible JSON files:

### Service Configuration

```typescript
interface ExportServiceConfig {
  // File paths
  xmlFilePath: string;              // Path to RZA XML export file
  outputDirectory?: string;         // Output directory for generated files

  // Export options
  exportArticles: boolean;          // Export articles/products
  exportCustomers: boolean;         // Export customers
  exportCategories: boolean;        // Export product categories
  exportGroups: boolean;           // Export article groups as categories

  // Output formats
  generateJsonFiles: boolean;       // Generate JSON output files
  generateCsvFiles: boolean;        // Generate CSV output files (future)
  logProgress: boolean;            // Enable progress logging

  // Domain-specific configurations
  articleConfig?: Partial<ArticleMappingConfig>;
  customerConfig?: Partial<CustomerMappingConfig>;
  categoryConfig?: Partial<CategoryMappingConfig>;
}
```

### Export Process Flow

1. **XML Parsing**: Parses RZA XML using `xml2js` library
2. **Data Extraction**: Extracts articles, customers, categories, and groups
3. **Domain Mapping**: Applies domain-specific mappers with configurations
4. **File Generation**: Creates JSON/CSV output files in specified directory
5. **Result Reporting**: Returns comprehensive statistics and file paths

### Export Result Structure

```typescript
interface ExportResult {
  success: boolean;
  message: string;
  stats: {
    articlesProcessed: number;
    customersProcessed: number;
    categoriesProcessed: number;
    groupsProcessed: number;
    errors: string[];
  };
  outputFiles: string[];
}
```

### Generated Output Files

The service generates the following files in the output directory:
- `woocommerce-products.json` - Mapped product data
- `woocommerce-customers.json` - Mapped customer data
- `woocommerce-categories.json` - Mapped category data
- `woocommerce-groups.json` - Mapped article group categories

### Error Handling

The service provides comprehensive error handling:
- **XML Parsing Errors**: Invalid XML structure or encoding issues
- **Mapping Errors**: Data transformation failures with detailed error messages
- **File System Errors**: Directory creation and file writing issues
- **Validation Errors**: Missing required fields or invalid data formats

Each error is captured with context and included in the export result for debugging.

## 📋 RZA XML Data Structure

The system processes RZA XML exports with the following structure:

### XML Root Elements
```xml
<root>
  <articles>          <!-- Product/article data -->
  <customers>         <!-- Customer/address data -->
  <orders>           <!-- Order status updates (ignored) -->
  <groupDefinition>  <!-- Article groups and sub-groups -->
  <categories>       <!-- Product categories -->
  <countries>        <!-- Country definitions (ignored) -->
</root>
```

### Article XML Structure
```xml
<article>
  <ordernumber>GRASS-HOPPER</ordernumber>
  <rzaArtikelID>774</rzaArtikelID>
  <artGroupID>1</artGroupID>
  <artSubGroupID>1</artSubGroupID>
  <categories>
    <category>271</category>
  </categories>
  <name>Green Hippo Gras Hoper</name>
  <ean>0</ean>
  <barcode></barcode>
  <unitID>stk</unitID>
  <instock>0</instock>
  <stock>0</stock>
  <weight>0.000</weight>
  <active>1</active>
  <onlineshopstatus>1</onlineshopstatus>
  <description_long><![CDATA[<p>Product description...</p>]]></description_long>
  <tax>20.00</tax>
  <taxRates>
    <taxRate>
      <countryID>1</countryID>
      <tax>19.00</tax>
    </taxRate>
  </taxRates>
  <suppliernumbers>
    <suppliernumber>Test1</suppliernumber>
  </suppliernumbers>
  <shippingtime></shippingtime>
  <fields>
    <field fieldnumber="1">Test1</field>
    <!-- ... up to field 10 -->
  </fields>
  <translations>
    <translation>
      <languageId>1</languageId>
      <name>Product Name - English</name>
      <longdescription><![CDATA[<p>English description...</p>]]></longdescription>
    </translation>
  </translations>
  <prices>
    <price>
      <price>10.75</price>
      <pricegroup>VK-Preis</pricegroup>
      <from>1</from>
    </price>
    <price>
      <price>8.84</price>
      <pricegroup>VK-Preis</pricegroup>
      <from>10</from>
    </price>
  </prices>
  <textilartikel>
    <kennzeichen>1</kennzeichen>
    <textilartikelID></textilartikelID>
    <!-- ... textile-specific fields -->
  </textilartikel>
</article>
```

### Customer XML Structure
```xml
<customer>
  <rzaAddressId>12345</rzaAddressId>
  <customernumber>CUST001</customernumber>
  <pricegroup>Standard VK</pricegroup>
  <fields>
    <field fieldnumber="1">Custom Value</field>
    <!-- ... up to field 5 -->
  </fields>
  <totaldiscount>
    <discount>5.00</discount>
    <from>2023-01-01</from>
    <to>2023-12-31</to>
  </totaldiscount>
  <Anrede>Herr</Anrede>
  <Titel>Dr.</Titel>
  <Zuname>Mustermann</Zuname>
  <Vorname>Max</Vorname>
  <Namenszeile2>Additional Name</Namenszeile2>
  <Namenszeile3>Company GmbH</Namenszeile3>
  <Land>Österreich</Land>
  <PLZ>1010</PLZ>
  <Ort>Wien</Ort>
  <Strasse>Musterstraße 1</Strasse>
  <UID>ATU12345678</UID>
  <Mail><EMAIL></Mail>
  <Telefon1>+43 1 1234567</Telefon1>
  <Telefon2>+43 664 1234567</Telefon2>
  <Fax>+43 1 1234568</Fax>
  <Kreditlimit>5000</Kreditlimit>
  <Liefersperre>0</Liefersperre>
  <countryID>22</countryID>
  <priceagreements>
    <agreement>
      <rzaArtikelID>774</rzaArtikelID>
      <artGroupID>1</artGroupID>
      <artSubGroupID>1</artSubGroupID>
      <discount>10.00</discount>
      <price>9.50</price>
      <from>5</from>
      <fromDate>2023-01-01</fromDate>
      <toDate>2023-12-31</toDate>
    </agreement>
  </priceagreements>
</customer>
```

## 🧪 Testing and Validation

### Test Suite

The `testExport.ts` file provides comprehensive testing and demonstration functionality:

```bash
# Run TypeScript compilation test
npx tsc --noEmit backend/sync/rza/export/testExport.ts

# Run the test demonstrations
node -r ts-node/register backend/sync/rza/export/testExport.ts
```

### Test Functions

The test suite includes the following test functions:

#### Article Mapping Test
```typescript
async function testArticleMapping() {
  const testArticle = createTestArticle();
  const testCategories = createTestCategories();

  const config: Partial<ArticleMappingConfig> = {
    defaultPriceGroup: 'VK-Preis',
    includeInactiveProducts: false,
    stockThreshold: 5
  };

  const wooProduct = mapRzaArticleToWooProduct(testArticle, testCategories, config);
  console.log('Mapped Product:', wooProduct);
}
```

#### Customer Mapping Test
```typescript
async function testCustomerMapping() {
  const testCustomer = createTestCustomer();

  const config: Partial<CustomerMappingConfig> = {
    defaultRole: 'customer',
    createUsername: true,
    syncBillingAddress: true
  };

  const wooCustomer = mapRzaCustomerToWooCustomer(testCustomer, config);
  console.log('Mapped Customer:', wooCustomer);
}
```

#### Category Mapping Test
```typescript
async function testCategoryMapping() {
  const testCategories = createTestCategories();
  const testGroups = createTestGroupDefinition();

  const categoryConfig: Partial<CategoryMappingConfig> = {
    createHierarchy: true,
    slugPrefix: 'rza-',
    includeDiscounts: true
  };

  const wooCategories = mapRzaCategoriesToWooCategories(testCategories, categoryConfig);
  const wooGroups = mapRzaGroupsToWooCategories(testGroups, categoryConfig);

  console.log('Mapped Categories:', wooCategories);
  console.log('Mapped Groups:', wooGroups);
}
```

#### Export Service Test
```typescript
async function testExportService() {
  const exportService = new RzaExportService({
    xmlFilePath: './full_export.example.xml',
    outputDirectory: './tmp/test-export',
    exportArticles: true,
    exportCustomers: true,
    exportCategories: true,
    exportGroups: true,
    generateJsonFiles: true,
    logProgress: true
  });

  const result = await exportService.executeExport();
  console.log('Export Result:', result);
}
```

### Validation Functions

The test suite includes validation functions to ensure data integrity:

- `validateArticleMapping()` - Validates article field mappings
- `validateCustomerMapping()` - Validates customer field mappings
- `validateCategoryHierarchy()` - Validates category hierarchy structure
- `validatePriceCalculations()` - Validates price mapping logic
- `validateMetaDataPreservation()` - Validates meta data preservation

## 🔄 Integration with WooCommerce

### WooCommerce REST API Integration

The generated JSON files are designed for direct integration with the WooCommerce REST API:

```typescript
// Example: Import products to WooCommerce
import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';

const WooCommerce = new WooCommerceRestApi({
  url: 'https://your-store.com',
  consumerKey: 'ck_...',
  consumerSecret: 'cs_...',
  version: 'wc/v3'
});

// Import products
const products = JSON.parse(fs.readFileSync('./tmp/woocommerce-products.json', 'utf8'));
for (const product of products) {
  try {
    const response = await WooCommerce.post('products', product);
    console.log(`Created product: ${response.data.name} (ID: ${response.data.id})`);
  } catch (error) {
    console.error(`Failed to create product ${product.name}:`, error.response?.data);
  }
}
```

### Batch Processing

For large datasets, use WooCommerce batch processing:

```typescript
// Batch create products (up to 100 per batch)
const batchSize = 100;
const batches = [];

for (let i = 0; i < products.length; i += batchSize) {
  batches.push(products.slice(i, i + batchSize));
}

for (const batch of batches) {
  const batchData = {
    create: batch
  };

  try {
    const response = await WooCommerce.post('products/batch', batchData);
    console.log(`Batch processed: ${response.data.create.length} products created`);
  } catch (error) {
    console.error('Batch processing failed:', error.response?.data);
  }
}
```

## 📝 Best Practices

### Data Validation
- Always validate RZA XML structure before processing
- Check for required fields (name, ordernumber, etc.)
- Validate price data and quantity breaks
- Ensure category references exist

### Error Handling
- Implement comprehensive error logging
- Use try-catch blocks for individual item processing
- Continue processing other items if one fails
- Provide detailed error messages with context

### Performance Optimization
- Process large datasets in batches
- Use streaming for very large XML files
- Implement progress reporting for long-running operations
- Cache category lookups to avoid repeated processing

### Data Integrity
- Preserve all RZA-specific data in meta fields
- Maintain bidirectional sync capability
- Use consistent naming conventions for meta fields
- Document all custom field mappings

### Configuration Management
- Use environment-specific configurations
- Validate configuration parameters
- Provide sensible defaults for optional settings
- Document all configuration options

## 🚨 Common Issues and Solutions

### XML Parsing Issues
**Problem**: XML parsing fails with encoding errors
**Solution**: Ensure XML file uses UTF-8 encoding and valid XML structure

### Missing Categories
**Problem**: Products reference non-existent categories
**Solution**: Process categories before products, or implement category auto-creation

### Price Calculation Errors
**Problem**: Incorrect price mapping or missing price groups
**Solution**: Validate price group configuration and implement fallback pricing

### Username Conflicts
**Problem**: Generated usernames already exist in WooCommerce
**Solution**: Implement username uniqueness checking and fallback generation

### Memory Issues
**Problem**: Out of memory errors with large XML files
**Solution**: Implement streaming XML parsing or process in smaller chunks

## 📚 Additional Resources

### RZA Documentation
- RZA ERP System Documentation
- XML Export Format Specification
- Field Definitions and Data Types

### WooCommerce Documentation
- [WooCommerce REST API Documentation](https://woocommerce.github.io/woocommerce-rest-api-docs/)
- [Product Schema Reference](https://woocommerce.github.io/woocommerce-rest-api-docs/#product-properties)
- [Customer Schema Reference](https://woocommerce.github.io/woocommerce-rest-api-docs/#customer-properties)
- [Category Schema Reference](https://woocommerce.github.io/woocommerce-rest-api-docs/#product-category-properties)

### TypeScript Resources
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Node.js TypeScript Best Practices](https://nodejs.org/en/docs/guides/nodejs-docker-webapp/)

---

*This documentation is maintained alongside the codebase. For the most current information, refer to the TypeScript interfaces and implementation files.*
