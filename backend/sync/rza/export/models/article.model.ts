/**
 * Article/Product model types for RZA export
 */

import { RzaField, RzaTaxRate, RzaTranslation, RzaPrice } from './common.model';

/**
 * Textile article specific information
 * Only relevant for textile products
 */
export interface RzaTextilartikel {
  kennzeichen?: string; // 1 = Main article, 2 = Textile article
  textilartikelID?: string; // Reference to main article ID
  groessentabelleID?: string; // Size table ID
  groessenID?: string; // Size ID
  farbtabelleID?: string; // Color table ID
  farbenID?: string; // Color ID
  saisonID?: string; // Season ID
  preiszuschlag?: string; // Price surcharge
}

/**
 * Complete RZA article/product information
 * Maps to WooCommerce products
 */
export interface RzaArticle {
  // Basic identification
  ordernumber: string; // Product SKU/order number
  rzaArtikelID: number; // Unique RZA article ID
  
  // Categorization
  artGroupID: number; // Article group ID
  artSubGroupID: number; // Article sub-group ID
  categories: number[]; // Product group IDs for online shop
  
  // Basic product information
  name: string; // Product name
  ean: string; // EAN barcode
  barcode: string; // Additional barcode
  unitID: string; // Unit of measurement (e.g., "stk", "kg")
  
  // Stock and availability
  instock: number; // Available quantity (not actual stock)
  stock: number; // Actual stock level
  weight: number; // Product weight
  
  // Status flags
  active: number; // 1 = active, 0 = inactive
  onlineshopstatus: number; // 1 = available in online shop, 0 = not available
  
  // Descriptions
  description_long: string; // Long product description (HTML)
  
  // Tax information
  tax: number; // Default tax rate
  taxRates: RzaTaxRate[]; // Tax rates per country
  
  // Supplier information
  suppliernumbers: string[]; // Manufacturer/supplier part numbers
  shippingtime: string; // Delivery time information
  
  // Custom fields and translations
  fields: RzaField[]; // Custom fields (max 10)
  translations: RzaTranslation[]; // Multi-language support
  
  // Pricing
  prices: RzaPrice[]; // Price configurations with quantity breaks
  
  // Textile-specific data
  textilartikel: RzaTextilartikel; // Only for textile products
}

/**
 * WooCommerce product interface for mapping
 */
export interface WooCommerceProduct {
  id?: number;
  name: string;
  slug?: string;
  type?: 'simple' | 'grouped' | 'external' | 'variable';
  status?: 'draft' | 'pending' | 'private' | 'publish';
  featured?: boolean;
  catalog_visibility?: 'visible' | 'catalog' | 'search' | 'hidden';
  description?: string;
  short_description?: string;
  sku?: string;
  price?: string;
  regular_price?: string;
  sale_price?: string;
  manage_stock?: boolean;
  stock_quantity?: number;
  stock_status?: 'instock' | 'outofstock' | 'onbackorder';
  categories?: Array<{ id: number; name?: string }>;
  images?: Array<{ src: string; alt?: string }>;
  weight?: string;
  dimensions?: {
    length?: string;
    width?: string;
    height?: string;
  };
  tax_status?: 'taxable' | 'shipping' | 'none';
  tax_class?: string;
  meta_data?: Array<{ key: string; value: string }>;
}

/**
 * Configuration for article mapping
 */
export interface ArticleMappingConfig {
  defaultPriceGroup: string; // Default price group to use (e.g., 'VK-Preis')
  defaultLanguageId: number; // Default language for translations
  includeInactiveProducts: boolean; // Whether to include inactive products
  stockThreshold: number; // Minimum stock to consider in stock
  defaultTaxClass: string; // Default WooCommerce tax class
  fieldToPropertyMapping: { [key: string]: string }; // Mapping of RZA fields to WooCommerce product properties
}
